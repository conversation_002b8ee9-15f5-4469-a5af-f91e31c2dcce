import 'package:flutter/material.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';

import '../../../../core/theme/app_theme.dart';
import '../widgets/weather_card.dart';
import '../widgets/health_status_card.dart';
import '../widgets/todo_list_card.dart';
import '../widgets/my_plants_section.dart';
import '../widgets/recent_care_records.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.background,
      body: CustomScrollView(
        slivers: [
          // 顶部导航栏
          SliverAppBar(
            floating: true,
            snap: true,
            backgroundColor: AppTheme.surface,
            foregroundColor: AppTheme.gray800,
            elevation: 0,
            shadowColor: Colors.transparent,
            surfaceTintColor: Colors.transparent,
            title: const Text(
              'logo',
              style: TextStyle(
                fontFamily: 'Pacifico',
                fontSize: 20,
                color: AppTheme.primary,
                fontWeight: FontWeight.normal,
              ),
            ),
            actions: [
              IconButton(
                icon: const Icon(Icons.notifications_outlined),
                onPressed: () {},
                color: AppTheme.gray600,
              ),
              IconButton(
                icon: const Icon(Icons.settings_outlined),
                onPressed: () {},
                color: AppTheme.gray600,
              ),
            ],
          ),
          
          // 主要内容
          SliverPadding(
            padding: const EdgeInsets.all(16),
            sliver: SliverList(
              delegate: SliverChildListDelegate([
                // 欢迎区域
                _buildWelcomeSection(),
                const SizedBox(height: 24),
                
                // 天气信息卡片
                const WeatherCard(),
                const SizedBox(height: 24),
                
                // 植物健康状态
                const HealthStatusCard(),
                const SizedBox(height: 24),
                
                // 待办事项
                const TodoListCard(),
                const SizedBox(height: 24),
                
                // 我的植物
                const MyPlantsSection(),
                const SizedBox(height: 24),
                
                // 最近养护记录
                const RecentCareRecords(),
                const SizedBox(height: 100), // 底部留白，避免被底部导航栏遮挡
              ]),
            ),
          ),
        ],
      ),
      
      // 添加新植物按钮
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // TODO: 导航到添加植物页面
        },
        backgroundColor: AppTheme.primary,
        foregroundColor: Colors.white,
        elevation: 8,
        child: Icon(MdiIcons.leaf),
      ),
    );
  }

  Widget _buildWelcomeSection() {
    final now = DateTime.now();
    final weekdays = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日'];
    final weekday = weekdays[now.weekday - 1];
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '你好，李园丁',
          style: AppTheme.heading2,
        ),
        const SizedBox(height: 4),
        Text(
          '今天是 ${now.year} 年 ${now.month} 月 ${now.day} 日，$weekday',
          style: AppTheme.bodySmall,
        ),
      ],
    );
  }
}
