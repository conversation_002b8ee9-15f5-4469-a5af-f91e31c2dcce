import 'package:flutter/material.dart';

import '../../../../core/theme/app_theme.dart';

class WeatherCard extends StatelessWidget {
  const WeatherCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.surface,
        borderRadius: AppTheme.cardRadius,
        boxShadow: const [AppTheme.cardShadow],
      ),
      child: Row(
        children: [
          // 天气图标
          Container(
            width: 48,
            height: 48,
            decoration: const BoxDecoration(
              color: AppTheme.yellow100,
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.wb_sunny,
              color: AppTheme.yellow500,
              size: 24,
            ),
          ),
          const SizedBox(width: 12),
          
          // 天气信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      '26°C',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                        color: AppTheme.gray800,
                      ),
                    ),
                    Text(
                      '北京市朝阳区',
                      style: AppTheme.bodySmall,
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Text(
                      '晴朗',
                      style: AppTheme.bodyMedium.copyWith(
                        color: AppTheme.gray600,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      '湿度: 65%',
                      style: AppTheme.bodyMedium.copyWith(
                        color: AppTheme.gray600,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      '适合植物生长',
                      style: AppTheme.bodyMedium.copyWith(
                        color: AppTheme.green500,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
