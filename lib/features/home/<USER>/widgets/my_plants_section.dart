import 'package:flutter/material.dart';

import '../../../../core/theme/app_theme.dart';

class PlantItem {
  final String id;
  final String name;
  final String careTime;
  final String imageUrl;

  PlantItem({
    required this.id,
    required this.name,
    required this.careTime,
    required this.imageUrl,
  });
}

class MyPlantsSection extends StatelessWidget {
  const MyPlantsSection({super.key});

  @override
  Widget build(BuildContext context) {
    final plants = [
      PlantItem(
        id: '1',
        name: '绿萝',
        careTime: '养护 45 天',
        imageUrl: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=300&h=300&fit=crop',
      ),
      PlantItem(
        id: '2',
        name: '多肉植物',
        careTime: '养护 30 天',
        imageUrl: 'https://images.unsplash.com/photo-1459411621453-7b03977f4bfc?w=300&h=300&fit=crop',
      ),
      PlantItem(
        id: '3',
        name: '白掌',
        careTime: '养护 60 天',
        imageUrl: 'https://images.unsplash.com/photo-1485955900006-10f4d324d411?w=300&h=300&fit=crop',
      ),
      PlantItem(
        id: '4',
        name: '虎皮兰',
        careTime: '养护 20 天',
        imageUrl: 'https://images.unsplash.com/photo-1509423350716-97f2360af2e4?w=300&h=300&fit=crop',
      ),
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              '我的植物',
              style: AppTheme.heading3,
            ),
            TextButton(
              onPressed: () {
                // TODO: 导航到全部植物页面
              },
              child: const Text(
                '查看全部',
                style: TextStyle(
                  color: AppTheme.primary,
                  fontSize: 14,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        SizedBox(
          height: 180,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.only(bottom: 8),
            itemCount: plants.length,
            itemBuilder: (context, index) {
              final plant = plants[index];
              return Padding(
                padding: EdgeInsets.only(
                  right: index < plants.length - 1 ? 12 : 0,
                ),
                child: _buildPlantCard(plant),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildPlantCard(PlantItem plant) {
    return InkWell(
      onTap: () {
        // TODO: 导航到植物详情页面
      },
      borderRadius: AppTheme.cardRadius,
      child: Container(
        width: 128,
        decoration: BoxDecoration(
          color: AppTheme.surface,
          borderRadius: AppTheme.cardRadius,
          boxShadow: const [AppTheme.cardShadow],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 植物图片
            ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
              child: Container(
                height: 128,
                width: double.infinity,
                color: AppTheme.gray100,
                child: plant.imageUrl.isNotEmpty
                    ? Image.network(
                        plant.imageUrl,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return _buildPlaceholderImage();
                        },
                        loadingBuilder: (context, child, loadingProgress) {
                          if (loadingProgress == null) return child;
                          return _buildPlaceholderImage();
                        },
                      )
                    : _buildPlaceholderImage(),
              ),
            ),
            
            // 植物信息
            Padding(
              padding: const EdgeInsets.all(8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    plant.name,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: AppTheme.gray800,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 2),
                  Text(
                    plant.careTime,
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppTheme.gray500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlaceholderImage() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: AppTheme.gray100,
      child: const Icon(
        Icons.local_florist,
        color: AppTheme.gray400,
        size: 32,
      ),
    );
  }
}
