import 'package:flutter/material.dart';
import 'dart:math' as math;

import '../../../../core/theme/app_theme.dart';

class HealthStatusCard extends StatelessWidget {
  const HealthStatusCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '植物健康状态',
          style: AppTheme.heading3,
        ),
        const SizedB<PERSON>(height: 12),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppTheme.surface,
            borderRadius: AppTheme.cardRadius,
            boxShadow: const [AppTheme.cardShadow],
          ),
          child: Column(
            children: [
              // 健康度圆环和详情按钮
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      // 圆环进度条
                      SizedBox(
                        width: 64,
                        height: 64,
                        child: CustomPaint(
                          painter: CircularProgressPainter(
                            progress: 0.85,
                            strokeWidth: 8,
                            backgroundColor: AppTheme.gray200,
                            progressColor: AppTheme.primary,
                          ),
                          child: const Center(
                            child: Text(
                              '85%',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                                color: AppTheme.gray800,
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      const Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '85%',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w500,
                              color: AppTheme.gray800,
                            ),
                          ),
                          Text(
                            '整体健康度',
                            style: AppTheme.bodySmall,
                          ),
                        ],
                      ),
                    ],
                  ),
                  TextButton(
                    onPressed: () {
                      // TODO: 导航到详情页面
                    },
                    child: const Text(
                      '查看详情',
                      style: TextStyle(
                        color: AppTheme.primary,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              
              // 环境指标
              Row(
                children: [
                  Expanded(
                    child: _buildEnvironmentIndicator(
                      icon: Icons.thermostat,
                      iconColor: AppTheme.orange500,
                      label: '温度',
                      status: '适宜',
                      backgroundColor: AppTheme.gray50,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildEnvironmentIndicator(
                      icon: Icons.water_drop,
                      iconColor: AppTheme.blue500,
                      label: '湿度',
                      status: '偏低',
                      backgroundColor: AppTheme.gray50,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildEnvironmentIndicator(
                      icon: Icons.wb_sunny,
                      iconColor: AppTheme.yellow500,
                      label: '光照',
                      status: '充足',
                      backgroundColor: AppTheme.gray50,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildEnvironmentIndicator({
    required IconData icon,
    required Color iconColor,
    required String label,
    required String status,
    required Color backgroundColor,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: const BoxDecoration(
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: iconColor,
              size: 20,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: AppTheme.gray800,
            ),
          ),
          Text(
            status,
            style: const TextStyle(
              fontSize: 12,
              color: AppTheme.gray500,
            ),
          ),
        ],
      ),
    );
  }
}

class CircularProgressPainter extends CustomPainter {
  final double progress;
  final double strokeWidth;
  final Color backgroundColor;
  final Color progressColor;

  CircularProgressPainter({
    required this.progress,
    required this.strokeWidth,
    required this.backgroundColor,
    required this.progressColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = (size.width - strokeWidth) / 2;

    // 背景圆环
    final backgroundPaint = Paint()
      ..color = backgroundColor
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    canvas.drawCircle(center, radius, backgroundPaint);

    // 进度圆环
    final progressPaint = Paint()
      ..color = progressColor
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    final sweepAngle = 2 * math.pi * progress;
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      -math.pi / 2, // 从顶部开始
      sweepAngle,
      false,
      progressPaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate != this;
  }
}
