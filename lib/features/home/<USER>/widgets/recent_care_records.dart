import 'package:flutter/material.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';

import '../../../../core/theme/app_theme.dart';

class CareRecord {
  final String id;
  final String type;
  final String plantName;
  final String description;
  final String time;
  final IconData icon;
  final Color iconColor;
  final Color backgroundColor;

  CareRecord({
    required this.id,
    required this.type,
    required this.plantName,
    required this.description,
    required this.time,
    required this.icon,
    required this.iconColor,
    required this.backgroundColor,
  });
}

class RecentCareRecords extends StatelessWidget {
  const RecentCareRecords({super.key});

  @override
  Widget build(BuildContext context) {
    final records = [
      CareRecord(
        id: '1',
        type: '浇水',
        plantName: '绿萝',
        description: '200ml 水',
        time: '今天 08:30',
        icon: Icons.water_drop,
        iconColor: AppTheme.blue500,
        backgroundColor: AppTheme.blue100,
      ),
      CareRecord(
        id: '2',
        type: '施肥',
        plantName: '多肉',
        description: '有机肥',
        time: '昨天 16:45',
        icon: MdiIcons.sprout,
        iconColor: AppTheme.green500,
        backgroundColor: AppTheme.green100,
      ),
      CareRecord(
        id: '3',
        type: '修剪',
        plantName: '白掌',
        description: '修剪枯叶',
        time: '7 月 4 日',
        icon: Icons.content_cut,
        iconColor: AppTheme.yellow500,
        backgroundColor: AppTheme.yellow100,
      ),
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              '最近养护记录',
              style: AppTheme.heading3,
            ),
            TextButton(
              onPressed: () {
                // TODO: 导航到全部记录页面
              },
              child: const Text(
                '查看全部',
                style: TextStyle(
                  color: AppTheme.primary,
                  fontSize: 14,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppTheme.surface,
            borderRadius: AppTheme.cardRadius,
            boxShadow: const [AppTheme.cardShadow],
          ),
          child: Column(
            children: records.asMap().entries.map((entry) {
              final index = entry.key;
              final record = entry.value;
              return Column(
                children: [
                  _buildCareRecordItem(record),
                  if (index < records.length - 1)
                    const SizedBox(height: 16),
                ],
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildCareRecordItem(CareRecord record) {
    return Row(
      children: [
        // 图标
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: record.backgroundColor,
            shape: BoxShape.circle,
          ),
          child: Icon(
            record.icon,
            color: record.iconColor,
            size: 20,
          ),
        ),
        const SizedBox(width: 12),
        
        // 内容
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    record.type,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: AppTheme.gray800,
                    ),
                  ),
                  Text(
                    record.time,
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppTheme.gray500,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 2),
              Text(
                '${record.plantName} - ${record.description}',
                style: const TextStyle(
                  fontSize: 12,
                  color: AppTheme.gray500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
