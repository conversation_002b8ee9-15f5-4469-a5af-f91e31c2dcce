import 'package:flutter/material.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';

import '../../core/theme/app_theme.dart';
import '../../features/home/<USER>/pages/home_page.dart';

class MainNavigation extends StatefulWidget {
  const MainNavigation({super.key});

  @override
  State<MainNavigation> createState() => _MainNavigationState();
}

class _MainNavigationState extends State<MainNavigation> {
  int _currentIndex = 0;

  final List<Widget> _pages = [
    const HomePage(),
    const PlaceholderPage(title: '植物'),
    const PlaceholderPage(title: '日历'),
    const PlaceholderPage(title: '我的'),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(
        index: _currentIndex,
        children: _pages,
      ),
      bottomNavigationBar: Container(
        decoration: const BoxDecoration(
          border: Border(
            top: BorderSide(color: AppTheme.gray200, width: 1),
          ),
        ),
        child: BottomNavigationBar(
          currentIndex: _currentIndex,
          onTap: (index) {
            setState(() {
              _currentIndex = index;
            });
          },
          type: BottomNavigationBarType.fixed,
          backgroundColor: AppTheme.surface,
          selectedItemColor: AppTheme.primary,
          unselectedItemColor: AppTheme.gray400,
          selectedFontSize: 12,
          unselectedFontSize: 12,
          elevation: 0,
          items: [
            BottomNavigationBarItem(
              icon: Icon(_currentIndex == 0 ? Icons.home : Icons.home_outlined),
              label: '首页',
            ),
            BottomNavigationBarItem(
              icon: Icon(_currentIndex == 1 ? MdiIcons.leaf : MdiIcons.leafOff),
              label: '植物',
            ),
            BottomNavigationBarItem(
              icon: Icon(_currentIndex == 2
                  ? Icons.calendar_today
                  : Icons.calendar_today_outlined),
              label: '日历',
            ),
            BottomNavigationBarItem(
              icon: Icon(
                  _currentIndex == 3 ? Icons.person : Icons.person_outline),
              label: '我的',
            ),
          ],
        ),
      ),
    );
  }
}

// 占位页面
class PlaceholderPage extends StatelessWidget {
  final String title;

  const PlaceholderPage({super.key, required this.title});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(title),
        backgroundColor: AppTheme.surface,
        foregroundColor: AppTheme.gray800,
        elevation: 0,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.construction,
              size: 64,
              color: AppTheme.gray400,
            ),
            const SizedBox(height: 16),
            Text(
              '$title页面',
              style: AppTheme.heading2.copyWith(color: AppTheme.gray600),
            ),
            const SizedBox(height: 8),
            Text(
              '正在开发中...',
              style: AppTheme.bodyMedium.copyWith(color: AppTheme.gray500),
            ),
          ],
        ),
      ),
    );
  }
}
